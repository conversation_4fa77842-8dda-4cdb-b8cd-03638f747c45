import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Send,
  Paperclip,
  Mic,
  AlertCircle,
  X,
  FileText,
  ChevronDown,
  Search,
  Lightbulb,
  Globe,
  Calendar,
  ArrowUp
} from 'lucide-react';
import { FileAttachment } from '@/types';
import { cn, formatFileSize } from '@/lib/utils';
import { useHotkeys } from 'react-hotkeys-hook';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';

interface ChatInputProps {
  onSendMessage: (message: string, files?: FileAttachment[], responseMode?: string) => void;
  attachedFiles: FileAttachment[];
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  onOpenEscalation: () => void;
  isLoading?: boolean;
  placeholder?: string;
  isEmpty?: boolean; // NEW: true if no messages
  onSummarizeFile?: (file: FileAttachment) => void;
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  attachedFiles,
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  onOpenEscalation,
  isLoading = false,
  placeholder = "Ask anything about leaves, benefits, or company policies...",
  isEmpty = false,
  onSummarizeFile,
}) => {
  const [message, setMessage] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [responseMode, setResponseMode] = useState('detailed'); // Default to detailed
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Keyboard shortcuts
  useHotkeys('cmd+enter,ctrl+enter', () => handleSubmit(), {
    enableOnFormTags: ['textarea'],
  });

  useHotkeys('cmd+k,ctrl+k', (e) => {
    e.preventDefault();
    fileInputRef.current?.click();
  });

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();
    const trimmed = message.trim();
    // If user asks to summarize and exactly one file is attached, call onSummarizeFile
    if (
      trimmed &&
      attachedFiles.length === 1 &&
      /summari[sz]e( this)? file/i.test(trimmed) &&
      typeof onSummarizeFile === 'function'
    ) {
      onSummarizeFile(attachedFiles[0]);
      setMessage('');
      if (textareaRef.current) textareaRef.current.style.height = 'auto';
      return;
    }
    // Otherwise, normal send
    if (trimmed && !isLoading) {
      onSendMessage(trimmed, attachedFiles, responseMode);
      setMessage('');
      if (textareaRef.current) textareaRef.current.style.height = 'auto';
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    files.forEach((file) => {
      const fileAttachment: FileAttachment = {
        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        name: file.name,
        size: file.size,
        type: file.type,
        file: file,
      };
      onAddFile(fileAttachment);
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const canSend = message.trim().length > 0 && !isLoading;

  const containerVariants = {
    focused: {
      boxShadow: "0 0 0 2px hsl(var(--ring))",
      transition: { duration: 0.2 }
    },
    unfocused: {
      boxShadow: "0 0 0 0px hsl(var(--ring))",
      transition: { duration: 0.2 }
    }
  };

  return (
    <div className={cn(
      "w-full max-w-2xl mx-auto flex flex-col items-center",
      isEmpty ? "justify-center flex-1 h-full" : "pb-6"
    )}>
      {/* File Attachments - Modern Design */}
      <AnimatePresence>
        {attachedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mb-4 flex flex-wrap gap-2 w-full"
          >
            {attachedFiles.map((file) => (
              <motion.div
                key={file.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-3 rounded-xl bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-3 text-sm shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="p-1.5 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <span className="font-medium text-gray-900 dark:text-gray-100 truncate block">
                    {file.name}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {formatFileSize(file.size)}
                  </span>
                </div>
                <button
                  onClick={() => onRemoveFile(file.id)}
                  className="p-1.5 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200 flex items-center justify-center group"
                  title="Remove file"
                >
                  <X className="h-4 w-4 text-gray-400 group-hover:text-red-500" />
                </button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Perplexity AI-style Chat Input */}
      <form
        onSubmit={handleSubmit}
        className="w-full flex items-center gap-3 rounded-2xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-4 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-blue-500/20 focus-within:border-blue-400"
        style={{ minHeight: 64 }}
      >
        {/* Left Icon Group - Search and Discovery */}
        <div className="flex items-center gap-1">
          <button
            type="button"
            className="p-2.5 rounded-xl transition-all duration-200 text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
            disabled={isLoading}
            tabIndex={-1}
            title="Search"
          >
            <Search className="h-5 w-5" />
          </button>
          <button
            type="button"
            className="p-2.5 rounded-xl transition-all duration-200 text-gray-500 dark:text-gray-400 hover:text-amber-600 dark:hover:text-amber-400 hover:bg-amber-50 dark:hover:bg-amber-900/20 focus:outline-none focus:ring-2 focus:ring-amber-500/20"
            disabled={isLoading}
            tabIndex={-1}
            title="Get suggestions"
          >
            <Lightbulb className="h-5 w-5" />
          </button>
        </div>

        {/* Main Input Area */}
        <div className="flex-1 min-w-0 px-2">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            onCompositionStart={() => setIsComposing(true)}
            onCompositionEnd={() => setIsComposing(false)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder={placeholder}
            disabled={isLoading}
            rows={1}
            className="w-full resize-none border-none bg-transparent focus:ring-0 text-base p-0 m-0 outline-none min-h-[28px] max-h-[200px] placeholder:text-gray-500 dark:placeholder:text-gray-400 text-gray-900 dark:text-white leading-relaxed"
            style={{ boxShadow: 'none' }}
          />
        </div>

        {/* Right Icon Group - Actions and Send */}
        <div className="flex items-center gap-1">
          <button
            type="button"
            className="p-2.5 rounded-xl transition-all duration-200 text-gray-500 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 focus:outline-none focus:ring-2 focus:ring-green-500/20"
            onClick={() => fileInputRef.current?.click()}
            disabled={isLoading}
            tabIndex={-1}
            title="Attach file"
          >
            <Paperclip className="h-5 w-5" />
          </button>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            className="hidden"
            onChange={handleFileUpload}
            accept=".pdf,.docx,.txt,.md,.csv,.xlsx"
          />
          <button
            type="button"
            className="p-2.5 rounded-xl transition-all duration-200 text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20 focus:outline-none focus:ring-2 focus:ring-purple-500/20"
            onClick={onOpenVoice}
            disabled={isLoading}
            tabIndex={-1}
            title="Voice input"
          >
            <Mic className="h-5 w-5" />
          </button>
          <button
            type="button"
            className="p-2.5 rounded-xl transition-all duration-200 text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-red-500/20"
            onClick={onOpenEscalation}
            disabled={isLoading}
            tabIndex={-1}
            title="Escalate to HR"
          >
            <AlertCircle className="h-5 w-5" />
          </button>

          {/* Send Button - Prominent and Modern */}
          <div className="ml-2">
            <button
              type="submit"
              className={cn(
                "p-2.5 rounded-xl transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2",
                canSend
                  ? "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white focus:ring-blue-500"
                  : "bg-gray-200 dark:bg-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed shadow-none hover:shadow-none transform-none hover:scale-100"
              )}
              disabled={!canSend}
              title="Send message"
            >
              <ArrowUp className="h-5 w-5" />
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ChatInput;
