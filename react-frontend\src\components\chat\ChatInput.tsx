import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Send,
  Paperclip,
  Mic,
  AlertCircle,
  X,
  FileText,
  ChevronDown
} from 'lucide-react';
import { FileAttachment } from '@/types';
import { cn, formatFileSize } from '@/lib/utils';
import { useHotkeys } from 'react-hotkeys-hook';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';

interface ChatInputProps {
  onSendMessage: (message: string, files?: FileAttachment[], responseMode?: string) => void;
  attachedFiles: FileAttachment[];
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  onOpenEscalation: () => void;
  isLoading?: boolean;
  placeholder?: string;
  isEmpty?: boolean; // NEW: true if no messages
  onSummarizeFile?: (file: FileAttachment) => void;
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  attachedFiles,
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  onOpenEscalation,
  isLoading = false,
  placeholder = "Ask anything about leaves, benefits, or company policies...",
  isEmpty = false,
  onSummarizeFile,
}) => {
  const [message, setMessage] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [responseMode, setResponseMode] = useState('detailed'); // Default to detailed
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Keyboard shortcuts
  useHotkeys('cmd+enter,ctrl+enter', () => handleSubmit(), {
    enableOnFormTags: ['textarea'],
  });

  useHotkeys('cmd+k,ctrl+k', (e) => {
    e.preventDefault();
    fileInputRef.current?.click();
  });

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();
    const trimmed = message.trim();
    // If user asks to summarize and exactly one file is attached, call onSummarizeFile
    if (
      trimmed &&
      attachedFiles.length === 1 &&
      /summari[sz]e( this)? file/i.test(trimmed) &&
      typeof onSummarizeFile === 'function'
    ) {
      onSummarizeFile(attachedFiles[0]);
      setMessage('');
      if (textareaRef.current) textareaRef.current.style.height = 'auto';
      return;
    }
    // Otherwise, normal send
    if (trimmed && !isLoading) {
      onSendMessage(trimmed, attachedFiles, responseMode);
      setMessage('');
      if (textareaRef.current) textareaRef.current.style.height = 'auto';
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    files.forEach((file) => {
      const fileAttachment: FileAttachment = {
        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        name: file.name,
        size: file.size,
        type: file.type,
        file: file,
      };
      onAddFile(fileAttachment);
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const canSend = message.trim().length > 0 && !isLoading;

  const containerVariants = {
    focused: {
      boxShadow: "0 0 0 2px hsl(var(--ring))",
      transition: { duration: 0.2 }
    },
    unfocused: {
      boxShadow: "0 0 0 0px hsl(var(--ring))",
      transition: { duration: 0.2 }
    }
  };

  return (
    <div className={cn(
      "w-full max-w-2xl mx-auto flex flex-col items-center",
      isEmpty ? "justify-center flex-1 h-full" : "pb-6"
    )}>
      {/* File Attachments */}
      <AnimatePresence>
        {attachedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mb-3 flex flex-wrap gap-2"
          >
            {attachedFiles.map((file) => (
              <motion.div
                key={file.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-2 rounded-lg bg-muted px-3 py-2 text-sm"
              >
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{file.name}</span>
                <span className="text-gray-500 dark:text-gray-400">
                  ({formatFileSize(file.size)})
                </span>
                <button
                  onClick={() => onRemoveFile(file.id)}
                  className="h-5 w-5 rounded hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-600 dark:hover:text-red-400 transition-colors flex items-center justify-center"
                >
                  <X className="h-3 w-3" />
                </button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* ChatGPT-style Chat Input */}
      <form
        onSubmit={handleSubmit}
        className="w-full flex items-center gap-2 rounded-2xl border border-gray-200 bg-white dark:bg-gray-800 px-4 py-3 shadow-sm focus-within:ring-2 focus-within:ring-blue-200"
        style={{ minHeight: 56 }}
      >
        {/* Left Icon Group (pilled background) */}
        <div className="flex items-center gap-1 bg-gray-100 dark:bg-gray-700 rounded-xl px-1 py-1 mr-2">
          <button
            type="button"
            className="p-2 rounded-xl transition-colors text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600"
            onClick={() => fileInputRef.current?.click()}
            disabled={isLoading}
            tabIndex={-1}
            title="Attach file"
          >
            <Paperclip className="h-5 w-5" />
          </button>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            className="hidden"
            onChange={handleFileUpload}
            accept=".pdf,.docx,.txt,.md,.csv,.xlsx"
          />
          {/* Add more left icons here if needed, e.g. search, lightbulb, etc. */}
        </div>

        {/* Main Input Area */}
        <div className="flex-1 min-w-0">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            onCompositionStart={() => setIsComposing(true)}
            onCompositionEnd={() => setIsComposing(false)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder={placeholder}
            disabled={isLoading}
            rows={1}
            className="w-full resize-none border-none bg-transparent focus:ring-0 text-base p-0 m-0 outline-none min-h-[24px] max-h-[200px] placeholder:text-gray-500 dark:placeholder:text-gray-400 text-gray-900 dark:text-white"
            style={{ boxShadow: 'none' }}
          />
        </div>

        {/* Right Icon Group */}
        <div className="flex items-center gap-2 ml-2">
          <button
            type="button"
            className="p-2 rounded-xl transition-colors text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600"
            onClick={onOpenVoice}
            disabled={isLoading}
            tabIndex={-1}
            title="Voice input"
          >
            <Mic className="h-5 w-5" />
          </button>
          <button
            type="button"
            className="p-2 rounded-xl transition-colors text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600"
            onClick={onOpenEscalation}
            disabled={isLoading}
            tabIndex={-1}
            title="Escalate to HR"
          >
            <AlertCircle className="h-5 w-5" />
          </button>
          {/* Send/Voice prominent button */}
          <button
            type="submit"
            className={cn(
              "p-2 rounded-xl transition-all duration-200 flex items-center justify-center",
              canSend
                ? "bg-teal-600 hover:bg-teal-700 text-white shadow-md"
                : "bg-gray-200 dark:bg-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed"
            )}
            disabled={!canSend}
            title="Send"
          >
            <Send className="h-5 w-5" />
          </button>
        </div>
      </form>
    </div>
  );
};

export default ChatInput;
