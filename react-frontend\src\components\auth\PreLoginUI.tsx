import React, { useState } from 'react';
import { Typewriter } from 'react-simple-typewriter';

interface PreLoginUIProps {
  onLogin: () => void;
  onRegister: () => void;
  onSendMessage: (message: string) => void;
}

const PreLoginUI: React.FC<PreLoginUIProps> = ({ onLogin, onRegister, onSendMessage }) => {
  const [inputValue, setInputValue] = useState('');
  const [showSendButton, setShowSendButton] = useState(false);

  const suggestions = [
    { text: "🗓️ Leave Policy", query: "What is the company's leave policy?" },
    { text: "👥 Referral Program", query: "How does the employee referral program work?" },
    { text: "👔 Dress Code", query: "What is the dress code policy?" },
    { text: "🏠 Work from Home", query: "Tell me about the work from home policy" },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInputValue(value);
    setShowSendButton(value.trim().length > 0);
    
    // Auto-resize textarea
    e.target.style.height = 'auto';
    e.target.style.height = e.target.scrollHeight + 'px';
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim()) {
      onSendMessage(inputValue.trim());
      setInputValue('');
      setShowSendButton(false);
    }
  };

  const handleSuggestionClick = (query: string) => {
    onSendMessage(query);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50" role="main" aria-label="Pre-login chat interface">
      {/* Header */}
      <header className="relative z-10 flex items-center justify-between px-4 sm:px-8 md:px-12 py-4 md:py-6 w-full" role="banner">
        {/* Logo aligned left */}
        <div className="flex items-center space-x-3" aria-label="ZiaHR logo and brand">
          <img src="/img/favicon.png" alt="ZiaHR logo" className="w-10 h-10 rounded-xl" />
          <span className="font-bold text-xl sm:text-2xl text-gray-900 dark:text-white">ZiaHR</span>
        </div>
        {/* Buttons aligned right */}
        <div className="flex items-center space-x-3">
          <button
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            onClick={onLogin}
            type="button"
          >
            Sign in
          </button>
          <button
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            onClick={onRegister}
            type="button"
          >
            <span className="hidden sm:inline">Get started</span>
            <span className="sm:hidden">Start</span>
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex flex-col items-center justify-center min-h-[calc(100vh-60px)] sm:min-h-[calc(100vh-80px)] lg:min-h-[calc(100vh-100px)] px-2 sm:px-4 md:px-8" aria-labelledby="prelogin-welcome-heading">
        {/* Hero Section */}
        <div className="text-center max-w-4xl mx-auto mb-2 sm:mb-3 lg:mb-4">
          <div className="mb-2 sm:mb-3">
            <h1 id="prelogin-welcome-heading" className="w-full text-center text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-1 sm:mb-2 leading-tight flex justify-center items-center gap-2 min-w-0">
              <span>Meet your new</span>
              <span className="inline-block bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent ml-2 min-w-0 max-w-[90vw] overflow-hidden text-ellipsis align-middle" aria-live="polite">
                <Typewriter
                  words={[
                    'HR assistant',
                    'leave balance checker',
                    'payslip retriever',
                    'policy guide',
                    'benefits expert',
                    'onboarding guide',
                    'exit support agent',
                  ]}
                  loop={0} // Infinite
                  cursor
                  cursorStyle="|"
                  typeSpeed={80}
                  deleteSpeed={60}
                  delaySpeed={1500}
                />
              </span>
            </h1>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed px-2 sm:px-0">
              Get instant answers about company policies, employee guidelines, and HR procedures. Available 24/7 to help you navigate your workplace questions.
            </p>
          </div>

          {/* Feature highlights */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6 sm:mb-8 lg:mb-10">
            <div className="flex flex-col items-center p-4">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-3">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="font-semibold text-sm text-gray-900 mb-1">Instant Answers</h3>
              <p className="text-xs text-gray-600 text-center">Get immediate responses to your HR questions</p>
            </div>
            <div className="flex flex-col items-center p-4">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-3">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-semibold text-sm text-gray-900 mb-1">Always Accurate</h3>
              <p className="text-xs text-gray-600 text-center">Information based on your company's policies</p>
            </div>
            <div className="flex flex-col items-center p-4">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-3">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="font-semibold text-sm text-gray-900 mb-1">Secure & Private</h3>
              <p className="text-xs text-gray-600 text-center">Your conversations are confidential</p>
            </div>
          </div>

          {/* Suggestion chips */}
          <div className="mb-6 sm:mb-8">
            <p className="text-xs text-gray-500 mb-2">Try asking about:</p>
            <div className="flex flex-wrap justify-center gap-2">
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  className="inline-flex items-center px-2.5 py-1 bg-white border border-gray-200 rounded-xl text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-sm"
                  onClick={() => handleSuggestionClick(suggestion.query)}
                  type="button"
                >
                  {suggestion.text}
                </button>
              ))}
            </div>
          </div>
        </div>
        {/* Chat Input */}
        <div className="w-full max-w-2xl mx-auto px-1 sm:px-0">
          <form onSubmit={handleSubmit} className="relative">
            <div className="flex items-end bg-white rounded-2xl shadow-lg border border-gray-200 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all duration-200">
              <div className="flex-1 p-4">
                <textarea
                  id="preLoginChatInput"
                  className="w-full bg-transparent border-none outline-none text-gray-900 placeholder-gray-500 resize-none text-sm leading-5"
                  placeholder="Ask me anything about HR policies, benefits, or procedures..."
                  rows={1}
                  aria-label="Type your question here"
                  aria-multiline="true"
                  value={inputValue}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  style={{ maxHeight: '100px', minWidth: '100%' }}
                />
              </div>
              <div className="flex items-center space-x-2 p-4 pt-2">
                <button
                  type="button"
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                  aria-label="Attach a file"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                  </svg>
                </button>
                <button
                  type="button"
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                  aria-label="Start voice input"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </button>
                {showSendButton && (
                  <button
                    type="submit"
                    className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                    title="Send message"
                    aria-label="Send message"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          </form>
          {/* Footer */}
          <div className="text-center mt-4">
            <p className="text-[10px] text-gray-500 px-2 sm:px-0">
              ZiaHR can make mistakes. Please verify important information with your HR department.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
};

export default PreLoginUI;
