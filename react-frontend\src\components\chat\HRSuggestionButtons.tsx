import React from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  FileText, 
  Users, 
  Home, 
  Briefcase, 
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface HRSuggestion {
  title: string;
  description: string;
  query: string;
  icon: React.ReactNode;
  color: string;
}

interface HRSuggestionButtonsProps {
  onSuggestionClick: (query: string) => void;
  className?: string;
}

const HRSuggestionButtons: React.FC<HRSuggestionButtonsProps> = ({
  onSuggestionClick,
  className
}) => {
  const suggestions: HRSuggestion[] = [
    {
      title: "Leave Policy",
      description: "Learn about vacation, sick leave, and time-off policies",
      query: "What is the company's leave policy and how do I request time off?",
      icon: <Calendar className="w-6 h-6" />,
      color: "bg-blue-500"
    },
    {
      title: "Referral Program", 
      description: "Understand how employee referrals work and rewards",
      query: "How does the employee referral program work and what are the benefits?",
      icon: <Users className="w-6 h-6" />,
      color: "bg-green-500"
    },
    {
      title: "Dress Code",
      description: "Check the company dress code and appearance guidelines",
      query: "What is the company dress code policy?",
      icon: <FileText className="w-6 h-6" />,
      color: "bg-purple-500"
    },
    {
      title: "Work from Home",
      description: "Remote work policies and hybrid work arrangements",
      query: "Tell me about the work from home and remote work policies",
      icon: <Home className="w-6 h-6" />,
      color: "bg-orange-500"
    },
    {
      title: "Benefits",
      description: "Explore health insurance, retirement, and other benefits",
      query: "What are the company benefits including health insurance and retirement plans?",
      icon: <Briefcase className="w-6 h-6" />,
      color: "bg-indigo-500"
    },
    {
      title: "Request Time Off",
      description: "Submit vacation requests and check your balance",
      query: "How do I request time off and check my vacation balance?",
      icon: <Clock className="w-6 h-6" />,
      color: "bg-red-500"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 120,
        damping: 20,
        mass: 0.8
      }
    },
    hover: {
      y: -8,
      scale: 1.03,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    },
    tap: {
      scale: 0.97,
      y: -4,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-w-5xl mx-auto", className)}
    >
      {suggestions.map((suggestion, index) => (
        <motion.button
          key={index}
          variants={cardVariants}
          whileHover="hover"
          whileTap="tap"
          onClick={() => onSuggestionClick(suggestion.query)}
          className={cn(
            "relative overflow-hidden rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700",
            "shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",
            "text-left focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-2",
            "group p-5 min-h-[120px] flex flex-col justify-between",
            "hover:border-blue-300 dark:hover:border-blue-600",
            className
          )}
        >
          {/* Background gradient overlay on hover */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/0 to-purple-50/0 group-hover:from-blue-50/50 group-hover:to-purple-50/30 dark:group-hover:from-blue-900/20 dark:group-hover:to-purple-900/10 transition-all duration-300" />

          <div className="relative z-10 flex items-start gap-3">
            <div className={cn(
              "p-2.5 rounded-xl text-white flex-shrink-0 shadow-md",
              suggestion.color,
              "group-hover:scale-110 group-hover:shadow-lg transition-all duration-300"
            )}>
              {React.cloneElement(suggestion.icon as React.ReactElement, { className: "w-5 h-5" })}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors text-sm leading-tight">
                {suggestion.title}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors">
                {suggestion.description}
              </p>
            </div>
          </div>

          {/* Subtle arrow indicator */}
          <div className="relative z-10 flex justify-end mt-3">
            <div className="w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-700 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 flex items-center justify-center transition-all duration-300 group-hover:scale-110">
              <svg className="w-3 h-3 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </motion.button>
      ))}
    </motion.div>
  );
};

export default HRSuggestionButtons;
