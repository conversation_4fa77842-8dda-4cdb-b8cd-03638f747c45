"""
Improved HR Assistant Prompt Template for Ziantrix HR Chatbot
"""

from langchain_core.prompts import (
    ChatPromptTemplate,
    MessagesPlaceholder,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate
)
from typing import List, Dict, Any
import json

from ..utils.logger import get_logger
logger = get_logger(__name__)


def create_hr_assistant_prompt(language: str = "English", 
                              response_mode: str = "auto",
                              include_reasoning: bool = False) -> ChatPromptTemplate:
    system_prompt = create_system_prompt(language, response_mode)

    return ChatPromptTemplate.from_messages([
        SystemMessagePromptTemplate.from_template(system_prompt),
        MessagesPlaceholder(variable_name="history"),
        SystemMessagePromptTemplate.from_template("Document Context:\n{context}"),
        HumanMessagePromptTemplate.from_template("{query}")
    ])


def create_system_prompt(language: str, response_mode: str) -> str:
    return f"""You are <PERSON><PERSON>, the AI HR Assistant for Ziantrix Technology Solutions.

## 🧠 Core Guidelines:
- Use only the provided document context. These may include HR policies, resumes, offer letters, contracts, performance reviews, or any HR-relevant content.
- Be exact: Use precise numbers, names, dates, entitlements, and legal references mentioned in the documents.
- Do not guess or use external knowledge.

## Supported Tasks:
You support the following types of tasks:
- **Document summarization** (e.g., summarize this resume or policy)
- **Text extraction** (e.g., extract the full education section from this document)
- **Entity extraction** (e.g., list all names, dates, and organizations in this contract)

## 📄 Task Instructions:

### Text Extraction:
If the user asks to "extract" or "get" part or all of a document, return:
- The exact text as found in the document
- Maintain the original wording and structure
- Avoid paraphrasing or summarizing

### Entity Extraction:
If the user asks to identify or extract entities, return:
- A list of all named entities (e.g., PERSON, ORG, DATE, LOCATION)
- Format like:
  - **John Doe** (PERSON)
  - **Ziantrix Technology Solutions** (ORG)
  - **January 2023** (DATE)

### Summarization Format:
When summarizing policies or resumes, use this format:
- **Purpose**
- **Scope**
- **Key Rules / Clauses**
- **Employee Responsibilities**
- **Consequences / Disciplinary Actions**
- **Support Mechanisms**
- **Legal References**

## 🧾 Response Formatting Rules:
- Use **headings** and **bold text** for important info
- Use bullet points (`-`) for clarity
- Use readable structure
- End with a clear next step or helpful suggestion

## ❓ Missing Info:
If a detail is not in the document:
- Clearly say: "This information is not available in the current documents"
- Suggest uploading a different file or contacting HR
- Never guess or assume

## 🗣️ Response Style: {get_response_mode_instructions(response_mode)}
## 🌐 Language: {language}
"""


RESPONSE_MODES: Dict[str, Dict[str, str]] = {
    "concise": {
        "label": "Concise",
        "instructions": "Keep it brief. Return key facts only. Limit to 2-3 bullet points or a short paragraph. Avoid examples and deep context."
    },
    "detailed": {
        "label": "Detailed",
        "instructions": "Provide thorough explanations, examples, and full context. Use structured sections and bullet points as needed."
    },
    "step_by_step": {
        "label": "Step-by-Step",
        "instructions": "Break down the answer into a clear, logical sequence of steps."
    },
    "auto": {
        "label": "Auto",
        "instructions": "Automatically determine the level of detail based on query complexity and user role."
    }
}

def get_response_mode_instructions(mode: str) -> str:
    mode = mode.strip().lower()
    if mode not in RESPONSE_MODES:
        # Optional: log or raise for unknown mode
        return RESPONSE_MODES["auto"]["instructions"]
    return RESPONSE_MODES[mode]["instructions"]


def create_formatted_response_template(response_type: str) -> str:
    templates = {
        "leave_policy": """
## Leave Policy Summary

{policy_details}

**Available Leave Types:**
{leave_types}

**Need Help?**
I can assist you with eligibility, entitlements, and leave applications.
""",
        "leave_balance": """
## Your Leave Balance

**Current Balance:**
- **Earned Leave:** {earned_leave} days
- **Sick Leave:** {sick_leave} days
- **Casual Leave:** {casual_leave} days

**Need Help?**
I can help explain how to apply or use your leave.
""",
        "policy_general": """
## {policy_name}

{policy_content}

**Key Points:**
{key_points}

**Questions?**
Feel free to ask for clarification on this policy.
""",
        "procedure": """
## {procedure_name}

**Steps to Follow:**
{steps}

**Required Documents:**
{documents}

**Timeline:** {timeline}

**Need Assistance?**
Ask me anything about this procedure.
""",
        "text_extraction": """
## Extracted Text

{text_content}

**Note:** This is the original document text as provided, without edits or summarization.
""",
        "entity_extraction": """
## Extracted Entities

{entities_list}

**Note:** These are the named entities found in the provided document.
"""
    }
    return templates.get(response_type, templates["policy_general"])


def create_error_response_template() -> str:
    return """
## ❌ Information Not Available

I'm not able to answer this based on the current documents. The information may not be included in the uploaded file.

**What you can do:**
- Upload another document with the relevant info
- Rephrase your query
- Contact HR directly

**I'm here to help** with anything present in the available documents.
"""


def create_context_prompt() -> str:
    return """
Based on the uploaded HR documents, answer the query by:

1. Only using information present in the context
2. Applying a clear, bullet-point structure
3. Including exact data (names, clauses, penalties)
4. Avoiding assumptions or external data

If the answer isn't present, say so clearly and suggest next steps.
"""


def detect_query_type(query: str) -> str:
    q = query.lower()
    if "leave" in q and "type" in q:
        return "leave_policy"
    if any(x in q for x in ["balance", "remaining", "days left"]):
        return "leave_balance"
    if any(x in q for x in ["apply", "how do i", "steps", "process", "procedure"]):
        return "procedure"
    if any(x in q for x in ["extract text", "get full text", "show complete text"]):
        return "text_extraction"
    if any(x in q for x in ["extract entities", "identify names", "highlight entities", "show all names", "show all dates"]):
        return "entity_extraction"
    if "policy" in q or "rules" in q:
        return "policy_general"
    return "general"


def detect_response_mode(query: str, intent: str = "unknown", confidence: float = 0.0) -> str:
    """
    Dynamically detect the best response mode for a query.
    Modes: auto, concise, detailed, step_by_step, empathetic
    """
    q = query.lower()
    if any(x in q for x in ["step by step", "steps", "procedure", "process", "how do i"]):
        return "step_by_step"
    if any(x in q for x in ["summary", "summarize", "brief", "short version"]):
        return "concise"
    if any(x in q for x in ["explain", "details", "detailed", "comprehensive", "why", "how"]):
        return "detailed"
    if any(x in q for x in ["feeling", "worried", "concerned", "problem", "issue", "complaint", "termination", "salary", "harassment", "support"]):
        return "empathetic"
    if intent == "procedure":
        return "step_by_step"
    if intent == "summary":
        return "concise"
    if confidence < 0.5:
        return "detailed"
    return "auto"


def should_include_reasoning(query: str, intent: str = "unknown", confidence: float = 0.0) -> bool:
    """
    Decide if chain-of-thought reasoning should be included.
    """
    q = query.lower()
    # Trigger reasoning for complex, multi-part, or sensitive queries
    if any(x in q for x in ["why", "how", "explain", "reason", "steps", "process", "procedure", "conflict", "exception", "termination", "complaint", "appeal", "dispute", "difficult", "complex", "scenario", "case study"]):
        return True
    if confidence < 0.5:
        return True
    if intent in ["procedure", "policy_general", "escalation"]:
        return True
    return False


def validate_response_format(response: str) -> Dict[str, Any]:
    checks = {
        "has_headers": "##" in response,
        "has_bullets": "-" in response,
        "has_bold": "**" in response,
        "length_ok": 100 < len(response) < 2000,
        "no_hallucinations": not any(x in response.lower() for x in ["i think", "probably", "maybe"])
    }
    checks["well_structured"] = all(checks.values())
    return checks


def format_leave_types(leave_data: List[Dict[str, Any]]) -> str:
    formatted = []
    for leave in leave_data:
        formatted.append(f"""**{leave.get('type', 'Unknown')}**  
- **Eligibility:** {leave.get('eligibility', 'Not specified')}  
- **Entitlement:** {leave.get('entitlement', 'Not specified')}  
- **Requirements:** {leave.get('requirements', 'None')}""")
    return "\n\n".join(formatted)


# Exported Constants
LEAVE_BALANCE_TEMPLATE = """
## Leave Balance Overview

- **Earned Leave:** {earned_leave} days
- **Sick Leave:** {sick_leave} days
- **Casual Leave:** {casual_leave} days

Need help applying your leave? Just ask!
"""

POLICY_NOT_FOUND_TEMPLATE = """
## Policy Not Found

The requested policy wasn't found in the provided documents.

You can:
- Upload a different document
- Contact HR
- Ask about other available policies
"""

__all__ = [
    'create_hr_assistant_prompt',
    'create_system_prompt',
    'get_response_mode_instructions',
    'create_formatted_response_template',
    'create_error_response_template',
    'create_context_prompt',
    'detect_query_type',
    'validate_response_format',
    'format_leave_types',
    'LEAVE_BALANCE_TEMPLATE',
    'POLICY_NOT_FOUND_TEMPLATE',
    'detect_response_mode',
    'should_include_reasoning'
]
