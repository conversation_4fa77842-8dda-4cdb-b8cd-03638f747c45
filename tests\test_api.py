"""
Comprehensive API tests for the Flask application endpoints.
"""
import pytest
import json
import tempfile
import io
from unittest.mock import patch, Mock
from pathlib import Path

from app import create_app


class TestAPIEndpoints:
    """Test all API endpoints."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask app."""
        app = create_app()
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    @pytest.fixture
    def auth_headers(self):
        """Generate authentication headers."""
        return {
            'Authorization': 'Bearer test_jwt_token',
            'Content-Type': 'application/json'
        }
    
    def test_index_endpoint(self, client):
        """Test the main index endpoint."""
        response = client.get('/')
        assert response.status_code == 200
        assert b'ZiaHR' in response.data
    
    def test_query_endpoint_success(self, client, auth_headers):
        """Test successful query processing."""
        with patch('app.get_chain_builder') as mock_get_chain, \
             patch('app.get_history_manager') as mock_get_history:
            
            # Mock chain builder
            mock_chain = Mock()
            mock_chain.run_chain_sync.return_value = {
                "content": "Employees get 20 days of annual leave per year.",
                "language": "en",
                "sources": [{"content": "Leave policy", "score": 0.85}]
            }
            mock_get_chain.return_value = mock_chain
            
            # Mock history manager
            mock_history = Mock()
            mock_get_history.return_value = mock_history
            
            data = {
                "query": "What is the leave policy?",
                "device_id": "test_device_123"
            }
            
            response = client.post('/api/query', 
                                 data=json.dumps(data),
                                 headers=auth_headers)
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert "response" in result
            assert "sources" in result
            assert "response_time" in result

    def test_query_endpoint_with_response_mode(self, client, auth_headers):
        """Test query endpoint with response_mode parameter."""
        with patch('app.get_chain_builder') as mock_get_chain, \
             patch('app.get_history_manager') as mock_get_history:

            # Mock chain builder
            mock_chain = Mock()
            mock_chain.run_chain_sync.return_value = {
                "content": "Brief response about leave policy.",
                "language": "en",
                "sources": [{"content": "Leave policy", "score": 0.85}],
                "response_mode": "concise"
            }
            mock_get_chain.return_value = mock_chain

            # Mock history manager
            mock_history = Mock()
            mock_get_history.return_value = mock_history

            data = {
                "query": "What is the leave policy?",
                "device_id": "test_device_123",
                "response_mode": "concise"
            }

            response = client.post('/api/query',
                                 data=json.dumps(data),
                                 headers=auth_headers)

            assert response.status_code == 200
            result = json.loads(response.data)
            assert "response" in result
            assert "sources" in result
            assert "response_time" in result
            assert "document_count" in result
    
    def test_query_endpoint_no_documents(self, client, auth_headers):
        """Test query when no documents are available."""
        with patch('app.get_chain_builder') as mock_get_chain, \
             patch('app.get_history_manager') as mock_get_history:
            
            # Mock chain builder with no documents
            mock_chain = Mock()
            mock_chain.get_vector_database_count.return_value = 0
            mock_chain.run_chain_sync.return_value = {
                "content": "I'm sorry, I couldn't find relevant information.",
                "language": "en",
                "sources": []
            }
            mock_get_chain.return_value = mock_chain
            
            # Mock history manager
            mock_history = Mock()
            mock_get_history.return_value = mock_history
            
            data = {
                "query": "What is the leave policy?",
                "device_id": "test_device_123"
            }
            
            response = client.post('/api/query', 
                                 data=json.dumps(data),
                                 headers=auth_headers)
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert "couldn't find relevant information" in result["response"]
    
    def test_query_endpoint_file_verification(self, client, auth_headers):
        """Test query with file verification."""
        with patch('app.get_chain_builder') as mock_get_chain, \
             patch('app.get_history_manager') as mock_get_history, \
             patch('app.TrainingPipeline') as mock_pipeline:
            
            # Mock chain builder
            mock_chain = Mock()
            mock_chain.run_chain_sync.return_value = {
                "content": "Test response",
                "language": "en",
                "sources": []
            }
            mock_get_chain.return_value = mock_chain
            
            # Mock history manager
            mock_history = Mock()
            mock_get_history.return_value = mock_history
            
            # Mock pipeline with unprocessed file
            mock_pipeline_instance = Mock()
            mock_pipeline_instance.is_file_processed.return_value = False
            mock_pipeline.return_value = mock_pipeline_instance
            
            data = {
                "query": "What is the leave policy?",
                "device_id": "test_device_123",
                "files_info": [{"name": "unprocessed.pdf"}]
            }
            
            response = client.post('/api/query', 
                                 data=json.dumps(data),
                                 headers=auth_headers)
            
            assert response.status_code == 400
            result = json.loads(response.data)
            assert "not processed" in result["error"]
    
    def test_query_endpoint_error_handling(self, client, auth_headers):
        """Test query endpoint error handling."""
        with patch('app.get_chain_builder') as mock_get_chain:
            # Mock chain builder that raises exception
            mock_chain = Mock()
            mock_chain.run_chain_sync.side_effect = Exception("Test error")
            mock_get_chain.return_value = mock_chain
            
            data = {
                "query": "What is the leave policy?",
                "device_id": "test_device_123"
            }
            
            response = client.post('/api/query', 
                                 data=json.dumps(data),
                                 headers=auth_headers)
            
            assert response.status_code == 500
            result = json.loads(response.data)
            assert "error" in result
            assert "Query processing failed" in result["error"]
    
    def test_speech_to_text_endpoint(self, client):
        """Test speech-to-text endpoint."""
        with patch('app.get_speech_to_text') as mock_get_stt:
            mock_stt = Mock()
            mock_stt.recognize_speech.return_value = "This is a test transcription"
            mock_get_stt.return_value = mock_stt
            
            response = client.post('/api/speech-to-text')
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result["text"] == "This is a test transcription"
    
    def test_speech_to_text_with_audio_file(self, client):
        """Test speech-to-text with uploaded audio file."""
        with patch('app.get_speech_to_text') as mock_get_stt:
            mock_stt = Mock()
            mock_get_stt.return_value = mock_stt
            
            # Create mock audio file
            audio_data = io.BytesIO(b"fake audio data")
            
            response = client.post('/api/speech-to-text',
                                 data={'audio': (audio_data, 'test.wav')},
                                 content_type='multipart/form-data')
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert "Audio file processing not implemented yet" in result["text"]
    
    def test_upload_document_endpoint_success(self, client, auth_headers):
        """Test successful document upload."""
        with patch('app.TrainingPipeline') as mock_pipeline:
            mock_pipeline_instance = Mock()
            mock_pipeline_instance.process_file.return_value = True
            mock_pipeline.return_value = mock_pipeline_instance
            
            # Create mock file
            file_data = io.BytesIO(b"Test document content")
            
            response = client.post('/api/upload-document',
                                 data={'file': (file_data, 'test_policy.txt')},
                                 headers=auth_headers,
                                 content_type='multipart/form-data')
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result["success"] == True
            assert result["filename"] == "test_policy.txt"
            assert result["processed"] == True
    
    def test_upload_document_no_file(self, client, auth_headers):
        """Test document upload without file."""
        response = client.post('/api/upload-document',
                             headers=auth_headers)
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result["error"] == "No file uploaded"
        assert result["success"] == False
    
    def test_upload_document_processing_failure(self, client, auth_headers):
        """Test document upload with processing failure."""
        with patch('app.TrainingPipeline') as mock_pipeline:
            mock_pipeline_instance = Mock()
            mock_pipeline_instance.process_file.return_value = False
            mock_pipeline.return_value = mock_pipeline_instance
            
            # Create mock file
            file_data = io.BytesIO(b"Test document content")
            
            response = client.post('/api/upload-document',
                                 data={'file': (file_data, 'test_policy.txt')},
                                 headers=auth_headers,
                                 content_type='multipart/form-data')
            
            assert response.status_code == 500
            result = json.loads(response.data)
            assert result["error"] == "Document processing failed"
            assert result["success"] == False
    
    def test_clear_history_endpoint(self, client, auth_headers):
        """Test clear history endpoint."""
        with patch('app.get_history_manager') as mock_get_history:
            mock_history = Mock()
            mock_get_history.return_value = mock_history
            
            data = {"device_id": "test_device_123"}
            
            response = client.post('/api/clear-history',
                                 data=json.dumps(data),
                                 headers=auth_headers)
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result["success"] == True
            mock_history.clear_history.assert_called_once_with("test_device_123")
    
    def test_confirm_escalation_endpoint_success(self, client, auth_headers):
        """Test successful escalation confirmation."""
        with patch('app.get_history_manager') as mock_get_history, \
             patch('app.get_email_service') as mock_get_email, \
             patch('app.ENABLE_EMAIL_ESCALATION', True), \
             patch('app.HR_EMAILS', ['<EMAIL>']):
            
            mock_history = Mock()
            mock_history.get_history.return_value = [
                {"user_query": "Test query", "assistant_response": "Test response"}
            ]
            mock_get_history.return_value = mock_history
            
            mock_email = Mock()
            mock_email.send_escalation_email.return_value = {"success": True}
            mock_get_email.return_value = mock_email
            
            data = {
                "query": "What is the leave policy?",
                "device_id": "test_device_123"
            }
            
            response = client.post('/api/confirm-escalation',
                                 data=json.dumps(data),
                                 headers=auth_headers)
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result["success"] == True
            assert "escalated to the HR team" in result["message"]
    
    def test_confirm_escalation_disabled(self, client, auth_headers):
        """Test escalation when email escalation is disabled."""
        with patch('app.ENABLE_EMAIL_ESCALATION', False):
            data = {
                "query": "What is the leave policy?",
                "device_id": "test_device_123"
            }
            
            response = client.post('/api/confirm-escalation',
                                 data=json.dumps(data),
                                 headers=auth_headers)
            
            assert response.status_code == 400
            result = json.loads(response.data)
            assert result["success"] == False
            assert "not enabled" in result["message"]
    
    def test_file_preview_endpoint_text(self, client, auth_headers):
        """Test file preview for text files."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is a test document content.")
            temp_file_path = f.name
        
        try:
            with patch('app.Path') as mock_path:
                mock_path.return_value.exists.return_value = True
                mock_path.return_value.suffix.lower.return_value = '.txt'
                mock_path.return_value.__truediv__.return_value = Path(temp_file_path)
                
                response = client.get('/api/file-preview?filename=test.txt',
                                    headers=auth_headers)
                
                assert response.status_code == 200
                result = json.loads(response.data)
                assert result["success"] == True
                assert result["content_type"] == "text"
                assert "test document content" in result["content"]
        finally:
            if Path(temp_file_path).exists():
                Path(temp_file_path).unlink()
    
    def test_file_preview_endpoint_not_found(self, client, auth_headers):
        """Test file preview for non-existent file."""
        with patch('app.Path') as mock_path:
            mock_path.return_value.exists.return_value = False
            
            response = client.get('/api/file-preview?filename=nonexistent.txt',
                                headers=auth_headers)
            
            assert response.status_code == 404
            result = json.loads(response.data)
            assert "not found" in result["error"]
    
    def test_document_health_check_endpoint(self, client):
        """Test document health check endpoint."""
        with patch('app.TrainingPipeline') as mock_pipeline, \
             patch('app.get_chain_builder') as mock_get_chain, \
             patch('app.Path') as mock_path:
            
            mock_pipeline_instance = Mock()
            mock_pipeline.return_value = mock_pipeline_instance
            
            mock_chain = Mock()
            mock_chain.get_vector_database_count.return_value = 100
            mock_get_chain.return_value = mock_chain
            
            # Mock file counts
            mock_path.return_value.glob.return_value = [Mock() for _ in range(5)]
            
            response = client.get('/api/health/documents')
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result["success"] == True
            assert "raw_files" in result
            assert "processed_files" in result
            assert "vector_embeddings" in result
            assert "processing_healthy" in result
    
    def test_process_hr_files_endpoint(self, client, auth_headers):
        """Test process HR files endpoint."""
        with patch('app.TrainingPipeline') as mock_pipeline:
            mock_pipeline_instance = Mock()
            mock_pipeline_instance.process_hr_files.return_value = 5
            mock_pipeline.return_value = mock_pipeline_instance
            
            data = {
                "directory": "Hr Files",
                "force_reprocess": False
            }
            
            response = client.post('/api/process-hr-files',
                                 data=json.dumps(data),
                                 headers=auth_headers)
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result["success"] == True
            assert result["files_processed"] == 5
            assert result["force_reprocess"] == False
    
    def test_register_endpoint_success(self, client):
        """Test successful user registration."""
        with patch('app.get_auth_service') as mock_get_auth:
            mock_auth = Mock()
            mock_auth.register_user.return_value = {
                "success": True,
                "message": "User registered successfully",
                "user_id": 1,
                "2fa_qr_url": "test_qr_url"
            }
            mock_get_auth.return_value = mock_auth
            
            data = {
                "email": "<EMAIL>",
                "password": "testpassword",
                "full_name": "Test User",
                "employee_id": "EMP001"
            }
            
            response = client.post('/api/register',
                                 data=json.dumps(data),
                                 content_type='application/json')
            
            assert response.status_code == 201
            result = json.loads(response.data)
            assert result["success"] == True
            assert result["message"] == "User registered successfully"
            assert "2fa_qr_url" in result
    
    def test_register_endpoint_missing_fields(self, client):
        """Test registration with missing required fields."""
        data = {
            "email": "<EMAIL>"
            # Missing password and full_name
        }
        
        response = client.post('/api/register',
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result["success"] == False
        assert "required" in result["message"]
    
    def test_logout_endpoint(self, client):
        """Test user logout."""
        with client.session_transaction() as sess:
            sess['user_id'] = 1
            sess['email'] = '<EMAIL>'
        
        response = client.post('/api/logout')
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result["success"] == True
        assert result["message"] == "Logged out successfully"
    
    def test_get_user_endpoint_authenticated(self, client):
        """Test get user info when authenticated."""
        with patch('app.get_auth_service') as mock_get_auth:
            mock_auth = Mock()
            mock_auth.user_model.get_user_by_id.return_value = {
                "id": 1,
                "email": "<EMAIL>",
                "full_name": "Test User",
                "company_name": "Test Company"
            }
            mock_get_auth.return_value = mock_auth
            
            with client.session_transaction() as sess:
                sess['user_id'] = 1
            
            response = client.get('/api/user')
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result["success"] == True
            assert result["user"]["email"] == "<EMAIL>"
            assert result["user"]["full_name"] == "Test User"
    
    def test_get_user_endpoint_not_authenticated(self, client):
        """Test get user info when not authenticated."""
        response = client.get('/api/user')
        
        assert response.status_code == 401
        result = json.loads(response.data)
        assert result["success"] == False
        assert result["message"] == "Not authenticated"
    
    def test_get_hr_representatives_endpoint(self, client):
        """Test get HR representatives endpoint."""
        response = client.get('/api/hr-representatives')
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result["success"] == True
        assert "representatives" in result
        assert len(result["representatives"]) > 0
    
    def test_submit_escalation_endpoint_success(self, client):
        """Test successful escalation submission."""
        with patch('app.get_email_service') as mock_get_email, \
             patch('app.ENABLE_EMAIL_ESCALATION', True):
            
            mock_email = Mock()
            mock_get_email.return_value = mock_email
            
            with client.session_transaction() as sess:
                sess['user_details'] = {
                    "id": 1,
                    "name": "Test User",
                    "email": "<EMAIL>"
                }
            
            data = {
                "hrPerson": "hr1",
                "issueType": "Leave Request",
                "issueDescription": "Need clarification on leave policy",
                "priority": "Medium"
            }
            
            response = client.post('/api/submit-escalation',
                                 data=json.dumps(data),
                                 content_type='application/json')
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result["success"] == True
            assert "escalated to HR" in result["message"]
            assert "escalation_id" in result
    
    def test_submit_escalation_missing_fields(self, client):
        """Test escalation submission with missing fields."""
        data = {
            "hrPerson": "hr1"
            # Missing required fields
        }
        
        response = client.post('/api/submit-escalation',
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result["success"] == False
        assert "Missing required field" in result["message"]
    
    def test_get_chat_endpoint(self, client, auth_headers):
        """Test get chat messages endpoint."""
        with patch('app.get_history_manager') as mock_get_history:
            mock_history = Mock()
            mock_history.get_chat_messages.return_value = [
                {
                    "user_query": "What is the leave policy?",
                    "assistant_response": "Employees get 20 days of leave.",
                    "timestamp": 1234567890.0
                }
            ]
            mock_history.get_chat_message_count.return_value = 1
            mock_get_history.return_value = mock_history
            
            response = client.get('/api/chats/test_chat_id?page=1&page_size=20',
                                headers=auth_headers)
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result["success"] == True
            assert "messages" in result
            assert "pagination" in result
            assert len(result["messages"]) == 1
    
    def test_get_chat_message_count_endpoint(self, client, auth_headers):
        """Test get chat message count endpoint."""
        with patch('app.get_history_manager') as mock_get_history:
            mock_history = Mock()
            mock_history.get_chat_message_count.return_value = 5
            mock_get_history.return_value = mock_history
            
            response = client.get('/api/chats/test_chat_id/count',
                                headers=auth_headers)
            
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result["success"] == True
            assert result["count"] == 5


class TestAPIErrorHandling:
    """Test API error handling."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask app."""
        app = create_app()
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    def test_invalid_json_request(self, client):
        """Test handling of invalid JSON requests."""
        response = client.post('/api/query',
                             data="invalid json",
                             content_type='application/json')
        
        assert response.status_code == 400
    
    def test_missing_required_fields(self, client):
        """Test handling of missing required fields."""
        data = {"query": "test"}  # Missing device_id
        
        response = client.post('/api/query',
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 400
    
    def test_service_unavailable(self, client):
        """Test handling when services are unavailable."""
        with patch('app.get_chain_builder') as mock_get_chain:
            mock_chain = Mock()
            mock_chain.run_chain_sync.side_effect = Exception("Service unavailable")
            mock_get_chain.return_value = mock_chain
            
            data = {
                "query": "What is the leave policy?",
                "device_id": "test_device_123"
            }
            
            response = client.post('/api/query',
                                 data=json.dumps(data),
                                 content_type='application/json')
            
            assert response.status_code == 500
    
    def test_file_upload_error(self, client):
        """Test file upload error handling."""
        with patch('app.TrainingPipeline') as mock_pipeline:
            mock_pipeline_instance = Mock()
            mock_pipeline_instance.process_file.side_effect = Exception("Processing error")
            mock_pipeline.return_value = mock_pipeline_instance
            
            file_data = io.BytesIO(b"Test content")
            
            response = client.post('/api/upload-document',
                                 data={'file': (file_data, 'test.txt')},
                                 content_type='multipart/form-data')
            
            assert response.status_code == 500


class TestAPIPerformance:
    """Test API performance."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask app."""
        app = create_app()
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    @pytest.mark.performance
    def test_query_response_time(self, client):
        """Test query response time."""
        import time
        
        with patch('app.get_chain_builder') as mock_get_chain, \
             patch('app.get_history_manager') as mock_get_history:
            
            mock_chain = Mock()
            mock_chain.run_chain_sync.return_value = {
                "content": "Test response",
                "language": "en",
                "sources": []
            }
            mock_get_chain.return_value = mock_chain
            
            mock_history = Mock()
            mock_get_history.return_value = mock_history
            
            data = {
                "query": "What is the leave policy?",
                "device_id": "test_device_123"
            }
            
            start_time = time.time()
            response = client.post('/api/query',
                                 data=json.dumps(data),
                                 content_type='application/json')
            end_time = time.time()
            
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 2.0  # Should respond within 2 seconds
    
    @pytest.mark.performance
    def test_concurrent_requests(self, client):
        """Test handling of concurrent requests."""
        import threading
        import time
        
        with patch('app.get_chain_builder') as mock_get_chain, \
             patch('app.get_history_manager') as mock_get_history:
            
            mock_chain = Mock()
            mock_chain.run_chain_sync.return_value = {
                "content": "Test response",
                "language": "en",
                "sources": []
            }
            mock_get_chain.return_value = mock_chain
            
            mock_history = Mock()
            mock_get_history.return_value = mock_history
            
            results = []
            errors = []
            
            def make_request():
                try:
                    data = {
                        "query": "What is the leave policy?",
                        "device_id": f"device_{threading.get_ident()}"
                    }
                    response = client.post('/api/query',
                                         data=json.dumps(data),
                                         content_type='application/json')
                    results.append(response.status_code)
                except Exception as e:
                    errors.append(str(e))
            
            # Start multiple threads
            threads = []
            for i in range(5):
                thread = threading.Thread(target=make_request)
                threads.append(thread)
                thread.start()
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
            
            # Verify all requests succeeded
            assert len(errors) == 0
            assert len(results) == 5
            assert all(status == 200 for status in results) 